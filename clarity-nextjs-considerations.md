# Clarity Visualize Next.js Specific Considerations

## Server-Side Rendering (SSR) Implications

### Why SSR Doesn't Work with Clarity Visualize

The `clarity-visualize` package requires browser-specific APIs and DOM manipulation that are not available during server-side rendering:

1. **Window Object**: The visualizer needs access to `window` and `document` objects
2. **Canvas API**: Uses HTML5 Canvas for rendering interactions and heatmaps
3. **iframe Manipulation**: Directly manipulates iframe content and styling
4. **Animation Frame**: Uses `requestAnimationFrame` for smooth playback

### Solutions for SSR

#### 1. Dynamic Imports with No SSR

```typescript
// components/ClarityReplayLoader.tsx
import dynamic from 'next/dynamic';
import { ComponentType } from 'react';
import { ClarityReplayProps } from '../types/clarity';

const ClarityReplay = dynamic(() => import('./ClarityReplay'), {
  ssr: false,
  loading: () => (
    <div className="clarity-loading">
      <div>Loading session replay...</div>
      <div className="spinner" />
    </div>
  )
}) as ComponentType<ClarityReplayProps>;

export default ClarityReplay;
```

#### 2. Conditional Rendering

```typescript
// components/ClarityReplayWrapper.tsx
import { useEffect, useState } from 'react';
import ClarityReplay from './ClarityReplay';
import { ClarityReplayProps } from '../types/clarity';

const ClarityReplayWrapper: React.FC<ClarityReplayProps> = (props) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="clarity-placeholder">
        <p>Session replay will load shortly...</p>
      </div>
    );
  }

  return <ClarityReplay {...props} />;
};

export default ClarityReplayWrapper;
```

#### 3. Using useIsomorphicLayoutEffect

```typescript
// hooks/useIsomorphicLayoutEffect.ts
import { useEffect, useLayoutEffect } from 'react';

const useIsomorphicLayoutEffect = 
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

export default useIsomorphicLayoutEffect;
```

## React Environment Handling

### State Management Considerations

```typescript
// hooks/useClarityReplayState.ts
import { useReducer, useCallback } from 'react';
import { Data } from 'clarity-decode';

interface ReplayState {
  isReady: boolean;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  error: string | null;
  decodedData: Data.DecodedPayload[];
}

type ReplayAction = 
  | { type: 'SET_READY'; payload: boolean }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_TIME'; payload: number }
  | { type: 'SET_DURATION'; payload: number }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_DATA'; payload: Data.DecodedPayload[] }
  | { type: 'RESET' };

const initialState: ReplayState = {
  isReady: false,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  error: null,
  decodedData: []
};

function replayReducer(state: ReplayState, action: ReplayAction): ReplayState {
  switch (action.type) {
    case 'SET_READY':
      return { ...state, isReady: action.payload };
    case 'SET_PLAYING':
      return { ...state, isPlaying: action.payload };
    case 'SET_TIME':
      return { ...state, currentTime: action.payload };
    case 'SET_DURATION':
      return { ...state, duration: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_DATA':
      return { ...state, decodedData: action.payload };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

export const useClarityReplayState = () => {
  const [state, dispatch] = useReducer(replayReducer, initialState);

  const setReady = useCallback((ready: boolean) => {
    dispatch({ type: 'SET_READY', payload: ready });
  }, []);

  const setPlaying = useCallback((playing: boolean) => {
    dispatch({ type: 'SET_PLAYING', payload: playing });
  }, []);

  const setTime = useCallback((time: number) => {
    dispatch({ type: 'SET_TIME', payload: time });
  }, []);

  const setDuration = useCallback((duration: number) => {
    dispatch({ type: 'SET_DURATION', payload: duration });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const setData = useCallback((data: Data.DecodedPayload[]) => {
    dispatch({ type: 'SET_DATA', payload: data });
  }, []);

  const reset = useCallback(() => {
    dispatch({ type: 'RESET' });
  }, []);

  return {
    state,
    actions: {
      setReady,
      setPlaying,
      setTime,
      setDuration,
      setError,
      setData,
      reset
    }
  };
};
```

### Context Provider for Global State

```typescript
// context/ClarityContext.tsx
import React, { createContext, useContext, ReactNode } from 'react';
import { useClarityReplayState } from '../hooks/useClarityReplayState';

interface ClarityContextType {
  state: ReturnType<typeof useClarityReplayState>['state'];
  actions: ReturnType<typeof useClarityReplayState>['actions'];
}

const ClarityContext = createContext<ClarityContextType | undefined>(undefined);

export const ClarityProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { state, actions } = useClarityReplayState();

  return (
    <ClarityContext.Provider value={{ state, actions }}>
      {children}
    </ClarityContext.Provider>
  );
};

export const useClarityContext = () => {
  const context = useContext(ClarityContext);
  if (context === undefined) {
    throw new Error('useClarityContext must be used within a ClarityProvider');
  }
  return context;
};
```

## Compatibility Issues and Workarounds

### 1. Module Resolution Issues

If you encounter module resolution issues, add to your `next.config.js`:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    esmExternals: false,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Ensure clarity packages are bundled for client-side
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    
    return config;
  },
  transpilePackages: ['clarity-decode', 'clarity-visualize'],
};

module.exports = nextConfig;
```

### 2. TypeScript Configuration

Update your `tsconfig.json` for better compatibility:

```json
{
  "compilerOptions": {
    "target": "es2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/types/*": ["./src/types/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}
```

### 3. Memory Leaks Prevention

```typescript
// hooks/useMemoryCleanup.ts
import { useEffect, useRef } from 'react';

export const useMemoryCleanup = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanup = (fn: () => void) => {
    cleanupFunctions.current.push(fn);
  };

  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(fn => {
        try {
          fn();
        } catch (error) {
          console.warn('Cleanup function failed:', error);
        }
      });
      cleanupFunctions.current = [];
    };
  }, []);

  return { addCleanup };
};

// Usage in ClarityReplay component
const { addCleanup } = useMemoryCleanup();

useEffect(() => {
  // Add cleanup for animation frames
  addCleanup(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  });

  // Add cleanup for event listeners
  addCleanup(() => {
    // Remove any event listeners
  });
}, [addCleanup]);
```

### 4. Performance Optimization

```typescript
// hooks/usePerformanceOptimization.ts
import { useCallback, useMemo } from 'react';
import { Data } from 'clarity-decode';

export const usePerformanceOptimization = (decodedData: Data.DecodedPayload[]) => {
  // Memoize heavy computations
  const processedData = useMemo(() => {
    if (!decodedData.length) return null;
    
    // Pre-process data for better performance
    return decodedData.map(payload => ({
      ...payload,
      // Add any preprocessing here
    }));
  }, [decodedData]);

  // Debounced seek function
  const debouncedSeek = useCallback(
    debounce((time: number, seekFn: (time: number) => void) => {
      seekFn(time);
    }, 100),
    []
  );

  return {
    processedData,
    debouncedSeek
  };
};

// Utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
```

### 5. Error Handling Best Practices

```typescript
// utils/errorHandling.ts
export class ClarityError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ClarityError';
  }
}

export const handleClarityError = (error: unknown): ClarityError => {
  if (error instanceof ClarityError) {
    return error;
  }

  if (error instanceof Error) {
    return new ClarityError(
      `Clarity visualization error: ${error.message}`,
      'VISUALIZATION_ERROR',
      error
    );
  }

  return new ClarityError(
    'Unknown Clarity error occurred',
    'UNKNOWN_ERROR'
  );
};

// Error reporting service
export const reportError = (error: ClarityError) => {
  // Send to your error reporting service
  console.error('Clarity Error:', {
    message: error.message,
    code: error.code,
    stack: error.stack,
    originalError: error.originalError
  });
};
```

## Testing Considerations

### Unit Testing with Jest

```typescript
// __tests__/ClarityReplay.test.tsx
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClarityReplay from '../components/ClarityReplay';

// Mock the clarity-visualize module
jest.mock('clarity-visualize', () => ({
  Visualizer: jest.fn().mockImplementation(() => ({
    setup: jest.fn().mockResolvedValue({}),
    merge: jest.fn().mockReturnValue({ events: [], dom: null }),
    render: jest.fn().mockResolvedValue(undefined),
    dom: jest.fn().mockResolvedValue(undefined)
  }))
}));

describe('ClarityReplay', () => {
  const mockDecodedData = [
    {
      envelope: { version: '0.8.20', sessionId: 'test', pageNum: 1 },
      timestamp: Date.now()
    }
  ];

  it('renders loading state initially', () => {
    render(<ClarityReplay decodedData={mockDecodedData} />);
    expect(screen.getByText('Loading session replay...')).toBeInTheDocument();
  });

  it('handles empty data gracefully', () => {
    render(<ClarityReplay decodedData={[]} />);
    expect(screen.getByText('Loading session replay...')).toBeInTheDocument();
  });
});
```

This comprehensive guide addresses the main Next.js specific considerations when integrating Clarity Visualize, including SSR handling, React environment considerations, compatibility issues, and testing strategies.
