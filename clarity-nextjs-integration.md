# Clarity Visualize Next.js Integration Guide

This guide demonstrates how to integrate the `clarity-visualize` package into a Next.js application to display session replays from decoded Clarity data.

## Installation

First, install the required packages:

```bash
npm install clarity-decode clarity-visualize
# or
yarn add clarity-decode clarity-visualize
```

## TypeScript Types

Create a types file for better TypeScript support:

```typescript
// types/clarity.ts
import { Data } from "clarity-decode";
import { Visualizer, Options, PlaybackState } from "clarity-visualize";

export interface ClarityReplayProps {
  decodedData: Data.DecodedPayload[];
  width?: number;
  height?: number;
  autoPlay?: boolean;
  onReady?: () => void;
  onError?: (error: Error) => void;
}

export interface ClarityReplayRef {
  play: () => void;
  pause: () => void;
  seek: (time: number) => void;
  reset: () => void;
  getVisualizer: () => Visualizer | null;
}
```

## Basic React Component

```typescript
// components/ClarityReplay.tsx
"use client";

import React, {
  useRef,
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { Visualizer } from "clarity-visualize";
import { Data } from "clarity-decode";
import { ClarityReplayProps, ClarityReplayRef } from "../types/clarity";

const ClarityReplay = forwardRef<ClarityReplayRef, ClarityReplayProps>(
  (
    {
      decodedData,
      width = 1920,
      height = 1080,
      autoPlay = false,
      onReady,
      onError,
    },
    ref
  ) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const visualizerRef = useRef<Visualizer | null>(null);
    const [isReady, setIsReady] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);
    const eventsRef = useRef<Data.DecodedEvent[]>([]);
    const animationFrameRef = useRef<number>();

    // Initialize visualizer
    useEffect(() => {
      if (
        !decodedData ||
        decodedData.length === 0 ||
        !iframeRef.current?.contentWindow
      ) {
        return;
      }

      const initializeVisualizer = async () => {
        try {
          const visualizer = new Visualizer();
          visualizerRef.current = visualizer;

          // Setup the visualizer with the iframe window
          await visualizer.setup(iframeRef.current!.contentWindow!, {
            version: decodedData[0].envelope.version,
            onresize: handleResize,
            mobile: false,
            vNext: true,
            locale: "en-us",
          });

          // Merge all decoded payloads and prepare events
          const merged = visualizer.merge(decodedData);
          eventsRef.current = merged.events.sort((a, b) => a.time - b.time);

          // Render initial DOM state
          if (merged.dom) {
            await visualizer.dom(merged.dom);
          }

          setIsReady(true);
          onReady?.();

          if (autoPlay) {
            startPlayback();
          }
        } catch (error) {
          console.error("Failed to initialize Clarity visualizer:", error);
          onError?.(error as Error);
        }
      };

      // Wait for iframe to load
      const iframe = iframeRef.current;
      if (iframe.contentDocument?.readyState === "complete") {
        initializeVisualizer();
      } else {
        iframe.onload = initializeVisualizer;
      }

      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    }, [decodedData, autoPlay, onReady, onError]);

    const handleResize = (newWidth: number, newHeight: number) => {
      if (!iframeRef.current) return;

      const iframe = iframeRef.current;
      const container = iframe.parentElement;
      if (!container) return;

      const margin = 10;
      const availableWidth = container.clientWidth - 2 * margin;
      const availableHeight = container.clientHeight - 2 * margin;
      const scale = Math.min(
        Math.min(availableWidth / newWidth, 1),
        Math.min(availableHeight / newHeight, 1)
      );

      iframe.style.position = "absolute";
      iframe.style.width = `${newWidth}px`;
      iframe.style.height = `${newHeight}px`;
      iframe.style.transformOrigin = "0 0 0";
      iframe.style.transform = `scale(${scale})`;
      iframe.style.border = "1px solid #cccccc";
      iframe.style.overflow = "hidden";
      iframe.style.left = `${(container.clientWidth - newWidth * scale) / 2}px`;
    };

    const startPlayback = () => {
      if (!visualizerRef.current || eventsRef.current.length === 0) return;

      setIsPlaying(true);
      const playFrame = () => {
        if (!isPlaying || eventsRef.current.length === 0) return;

        const event = eventsRef.current[0];
        const end = event.time + 16; // 60FPS => 16ms per frame
        let index = 0;

        while (
          eventsRef.current[index] &&
          eventsRef.current[index].time < end
        ) {
          index++;
        }

        const frameEvents = eventsRef.current.splice(0, index);
        if (frameEvents.length > 0) {
          visualizerRef.current?.render(frameEvents);
        }

        if (eventsRef.current.length > 0) {
          animationFrameRef.current = requestAnimationFrame(playFrame);
        } else {
          setIsPlaying(false);
        }
      };

      animationFrameRef.current = requestAnimationFrame(playFrame);
    };

    const pausePlayback = () => {
      setIsPlaying(false);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };

    const resetPlayback = () => {
      pausePlayback();
      if (visualizerRef.current && decodedData.length > 0) {
        const merged = visualizerRef.current.merge(decodedData);
        eventsRef.current = merged.events.sort((a, b) => a.time - b.time);
      }
    };

    const seekTo = (time: number) => {
      if (!visualizerRef.current || !decodedData.length) return;

      pausePlayback();
      const merged = visualizerRef.current.merge(decodedData);
      const allEvents = merged.events.sort((a, b) => a.time - b.time);

      // Find events up to the seek time
      const eventsToRender = allEvents.filter((event) => event.time <= time);
      const remainingEvents = allEvents.filter((event) => event.time > time);

      // Reset and render up to seek point
      visualizerRef.current
        .setup(iframeRef.current!.contentWindow!, {
          version: decodedData[0].envelope.version,
          onresize: handleResize,
          mobile: false,
          vNext: true,
          locale: "en-us",
        })
        .then(() => {
          if (merged.dom) {
            visualizerRef.current?.dom(merged.dom);
          }
          if (eventsToRender.length > 0) {
            visualizerRef.current?.render(eventsToRender);
          }
          eventsRef.current = remainingEvents;
        });
    };

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      play: startPlayback,
      pause: pausePlayback,
      seek: seekTo,
      reset: resetPlayback,
      getVisualizer: () => visualizerRef.current,
    }));

    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          minHeight: "400px",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          overflow: "hidden",
        }}
      >
        <iframe
          ref={iframeRef}
          title="Clarity Session Replay"
          style={{
            width: `${width}px`,
            height: `${height}px`,
            border: "none",
            display: isReady ? "block" : "none",
          }}
          sandbox="allow-same-origin allow-scripts"
        />
        {!isReady && (
          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              textAlign: "center",
            }}
          >
            <div>Loading session replay...</div>
          </div>
        )}
      </div>
    );
  }
);

ClarityReplay.displayName = "ClarityReplay";

export default ClarityReplay;
```

## Advanced Component with Tailwind Controls

````typescript
// components/ClarityReplayWithControls.tsx
'use client';

import React, { useRef, useState } from 'react';
import { Play, Pause, RotateCcw, Download, Maximize2, Minimize2 } from 'lucide-react';
import ClarityReplay from './ClarityReplay';
import { ClarityReplayRef, ClarityReplayProps } from '../types/clarity';

interface ClarityReplayWithControlsProps extends ClarityReplayProps {
  showControls?: boolean;
  className?: string;
}

const ClarityReplayWithControls: React.FC<ClarityReplayWithControlsProps> = ({
  showControls = true,
  className = '',
  ...replayProps
}) => {
  const replayRef = useRef<ClarityReplayRef>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handlePlay = () => {
    replayRef.current?.play();
    setIsPlaying(true);
  };

  const handlePause = () => {
    replayRef.current?.pause();
    setIsPlaying(false);
  };

  const handleReset = () => {
    replayRef.current?.reset();
    setIsPlaying(false);
  };

  const handleReady = () => {
    setIsReady(true);
    replayProps.onReady?.();
  };

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleDownload = () => {
    // Export session data functionality
    const visualizer = replayRef.current?.getVisualizer();
    if (visualizer && replayProps.decodedData) {
      const merged = visualizer.merge(replayProps.decodedData);
      const blob = new Blob([JSON.stringify(merged, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'clarity-session-data.json';
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden ${className} ${
      isFullscreen ? 'fixed inset-0 z-50' : ''
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="ml-4 text-sm font-medium text-gray-700">
            Session Replay
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleDownload}
            disabled={!isReady}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download session data"
          >
            <Download size={16} />
          </button>
          <button
            onClick={handleFullscreen}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
            title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        </div>
      </div>

      {/* Replay Container */}
      <div className="flex-1 relative bg-gray-100">
        <ClarityReplay
          ref={replayRef}
          {...replayProps}
          onReady={handleReady}
        />

        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading session replay...</p>
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && isReady && (
        <div className="flex items-center justify-center px-6 py-4 bg-white border-t border-gray-200 space-x-4">
          <button
            onClick={isPlaying ? handlePause : handlePlay}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isPlaying ? <Pause size={16} /> : <Play size={16} />}
            <span className="font-medium">{isPlaying ? 'Pause' : 'Play'}</span>
          </button>

          <button
            onClick={handleReset}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            <RotateCcw size={16} />
            <span className="font-medium">Reset</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default ClarityReplayWithControls;

## Usage Examples

### Basic Usage in a Next.js Page

```typescript
// pages/replay.tsx or app/replay/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { decode } from 'clarity-decode';
import ClarityReplayWithControls from '../components/ClarityReplayWithControls';
import { Data } from 'clarity-decode';

const ReplayPage: React.FC = () => {
  const [decodedData, setDecodedData] = useState<Data.DecodedPayload[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Load encoded Clarity data (from API, file, etc.)
    const loadClarityData = async () => {
      try {
        // Example: Load from API endpoint
        const response = await fetch('/api/clarity-data');
        const encodedPayloads = await response.json();

        // Decode the payloads
        const decoded = encodedPayloads.map((payload: string) => decode(payload));
        setDecodedData(decoded);
      } catch (err) {
        setError('Failed to load Clarity data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadClarityData();
  }, []);

  if (loading) {
    return <div>Loading session replay...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (decodedData.length === 0) {
    return <div>No session data available</div>;
  }

  return (
    <div style={{ height: '100vh', padding: '20px' }}>
      <h1>Clarity Session Replay</h1>
      <div style={{ height: 'calc(100vh - 100px)' }}>
        <ClarityReplayWithControls
          decodedData={decodedData}
          autoPlay={false}
          onReady={() => console.log('Replay ready!')}
          onError={(error) => console.error('Replay error:', error)}
        />
      </div>
    </div>
  );
};

export default ReplayPage;
````

### API Route for Serving Clarity Data

```typescript
// pages/api/clarity-data.ts or app/api/clarity-data/route.ts
import { NextApiRequest, NextApiResponse } from "next";

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Example encoded Clarity data - in practice, this would come from your database
  const encodedData = [
    '{"e":{"version":"0.8.20","sessionId":"abc123","pageNum":1},"a":[[1234567890,1,{"tag":"html","attributes":{},"children":[]}]]}',
    // ... more encoded payloads
  ];

  res.status(200).json(encodedData);
}

// For App Router (app/api/clarity-data/route.ts)
export async function GET() {
  const encodedData = [
    '{"e":{"version":"0.8.20","sessionId":"abc123","pageNum":1},"a":[[1234567890,1,{"tag":"html","attributes":{},"children":[]}]]}',
    // ... more encoded payloads
  ];

  return Response.json(encodedData);
}
```

### Custom Hook for Clarity Data Management

```typescript
// hooks/useClarityData.ts
import { useState, useEffect } from "react";
import { decode } from "clarity-decode";
import { Data } from "clarity-decode";

interface UseClarityDataOptions {
  sessionId?: string;
  autoLoad?: boolean;
}

export const useClarityData = (options: UseClarityDataOptions = {}) => {
  const [decodedData, setDecodedData] = useState<Data.DecodedPayload[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadData = async (sessionId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/clarity-data?sessionId=${sessionId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch data");
      }

      const encodedPayloads = await response.json();
      const decoded = encodedPayloads.map((payload: string) => decode(payload));
      setDecodedData(decoded);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (options.autoLoad && options.sessionId) {
      loadData(options.sessionId);
    }
  }, [options.autoLoad, options.sessionId]);

  return {
    decodedData,
    loading,
    error,
    loadData,
    refetch: () => options.sessionId && loadData(options.sessionId),
  };
};
```

## Next.js Specific Considerations

### 1. Client-Side Rendering Only

The Clarity visualizer must run on the client side since it manipulates DOM and uses browser APIs:

```typescript
// components/ClarityWrapper.tsx
import dynamic from "next/dynamic";

// Dynamically import with no SSR
const ClarityReplay = dynamic(() => import("./ClarityReplay"), {
  ssr: false,
  loading: () => <div>Loading replay component...</div>,
});

export default ClarityReplay;
```

### 2. CSS Styles and Tailwind Configuration

Add the required CSS for Clarity visualizations:

```css
/* styles/clarity.css */
iframe[data-clarity-unavailable-small],
iframe[data-clarity-unavailable],
img[data-clarity-blob-hide="sb"],
img[data-clarity-blob-hide="lb"],
img[data-clarity-hide="sb"],
img[data-clarity-hide="lb"] {
  background-color: #fbfbfe;
  border-style: dashed;
  border-width: 6px;
  border-color: #827dff;
}

/* Clarity-specific styles */
.clarity-replay-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.clarity-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa;
}

/* Pointer and interaction styles */
[data-clarity-pointer] {
  pointer-events: none;
  z-index: 2147483647;
}

[data-clarity-click] {
  pointer-events: none;
  z-index: 2147483646;
}
```

For Tailwind CSS users, add these custom styles to your `globals.css`:

```css
/* styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .clarity-replay-frame {
    @apply relative w-full h-full overflow-hidden bg-gray-50 rounded-lg border border-gray-200;
  }

  .clarity-loading-spinner {
    @apply animate-spin rounded-full border-b-2 border-blue-600;
  }

  .clarity-control-button {
    @apply flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .clarity-control-button-primary {
    @apply clarity-control-button bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500;
  }

  .clarity-control-button-secondary {
    @apply clarity-control-button bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500;
  }
}
```

Import in your `_app.tsx` or layout:

```typescript
// pages/_app.tsx or app/layout.tsx
import "../styles/globals.css";
```

### 3. Error Boundaries

Wrap the component in an error boundary:

```typescript
// components/ClarityErrorBoundary.tsx
import React, { Component, ReactNode } from "react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ClarityErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("Clarity replay error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div style={{ padding: "20px", textAlign: "center" }}>
            <h3>Session replay failed to load</h3>
            <p>Error: {this.state.error?.message}</p>
            <button onClick={() => this.setState({ hasError: false })}>
              Try Again
            </button>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

export default ClarityErrorBoundary;
```

### 4. Memory Management

For better performance, implement cleanup:

```typescript
// In your component
useEffect(() => {
  return () => {
    // Cleanup visualizer resources
    if (visualizerRef.current) {
      // The visualizer doesn't expose a cleanup method, but we can
      // clear references and cancel animations
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      visualizerRef.current = null;
    }
  };
}, []);
```

## Complete Integration Example

```typescript
// pages/session/[sessionId].tsx
import React from "react";
import { GetServerSideProps } from "next";
import dynamic from "next/dynamic";
import ClarityErrorBoundary from "../../components/ClarityErrorBoundary";
import { useClarityData } from "../../hooks/useClarityData";

const ClarityReplayWithControls = dynamic(
  () => import("../../components/ClarityReplayWithControls"),
  { ssr: false, loading: () => <div>Loading replay...</div> }
);

interface SessionPageProps {
  sessionId: string;
}

const SessionPage: React.FC<SessionPageProps> = ({ sessionId }) => {
  const { decodedData, loading, error } = useClarityData({
    sessionId,
    autoLoad: true,
  });

  return (
    <div style={{ height: "100vh" }}>
      <header style={{ padding: "16px", borderBottom: "1px solid #e0e0e0" }}>
        <h1>Session Replay: {sessionId}</h1>
      </header>

      <main style={{ height: "calc(100vh - 80px)" }}>
        <ClarityErrorBoundary>
          {loading && <div>Loading session data...</div>}
          {error && <div>Error: {error}</div>}
          {decodedData.length > 0 && (
            <ClarityReplayWithControls
              decodedData={decodedData}
              autoPlay={false}
              showControls={true}
            />
          )}
        </ClarityErrorBoundary>
      </main>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { sessionId } = context.params!;

  return {
    props: {
      sessionId: sessionId as string,
    },
  };
};

export default SessionPage;
```

## Advanced Features

### Timeline Scrubber Component

```typescript
// components/ClarityTimeline.tsx
"use client";

import React, { useState, useEffect, useRef } from "react";
import { Data } from "clarity-decode";

interface ClarityTimelineProps {
  decodedData: Data.DecodedPayload[];
  currentTime: number;
  onSeek: (time: number) => void;
  className?: string;
}

const ClarityTimeline: React.FC<ClarityTimelineProps> = ({
  decodedData,
  currentTime,
  onSeek,
  className = "",
}) => {
  const [duration, setDuration] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const timelineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (decodedData.length > 0) {
      // Calculate total duration from all events
      let maxTime = 0;
      decodedData.forEach((payload) => {
        Object.values(payload).forEach((events) => {
          if (Array.isArray(events)) {
            events.forEach((event) => {
              if (event.time > maxTime) {
                maxTime = event.time;
              }
            });
          }
        });
      });
      setDuration(maxTime);
    }
  }, [decodedData]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    handleSeek(e);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      handleSeek(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleSeek = (e: React.MouseEvent) => {
    if (!timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));
    const seekTime = percentage * duration;
    onSeek(seekTime);
  };

  const formatTime = (time: number) => {
    const seconds = Math.floor(time / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className={`w-full ${className}`}>
      <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>

      <div
        ref={timelineRef}
        className="relative h-2 bg-gray-200 rounded-full cursor-pointer"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div
          className="absolute top-0 left-0 h-full bg-blue-600 rounded-full transition-all duration-100"
          style={{ width: `${progressPercentage}%` }}
        />
        <div
          className="absolute top-1/2 w-4 h-4 bg-blue-600 rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-md"
          style={{ left: `${progressPercentage}%` }}
        />
      </div>
    </div>
  );
};

export default ClarityTimeline;
```

### Enhanced Replay Component with Timeline

```typescript
// components/ClarityReplayEnhanced.tsx
"use client";

import React, { useRef, useState, useCallback } from "react";
import {
  Play,
  Pause,
  RotateCcw,
  Download,
  Settings,
  Maximize2,
} from "lucide-react";
import ClarityReplay from "./ClarityReplay";
import ClarityTimeline from "./ClarityTimeline";
import { ClarityReplayRef, ClarityReplayProps } from "../types/clarity";

interface ClarityReplayEnhancedProps extends ClarityReplayProps {
  showTimeline?: boolean;
  showSettings?: boolean;
  playbackSpeed?: number;
  onPlaybackSpeedChange?: (speed: number) => void;
}

const ClarityReplayEnhanced: React.FC<ClarityReplayEnhancedProps> = ({
  showTimeline = true,
  showSettings = true,
  playbackSpeed = 1,
  onPlaybackSpeedChange,
  ...replayProps
}) => {
  const replayRef = useRef<ClarityReplayRef>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [showSpeedMenu, setShowSpeedMenu] = useState(false);

  const speedOptions = [0.25, 0.5, 1, 1.5, 2];

  const handlePlay = useCallback(() => {
    replayRef.current?.play();
    setIsPlaying(true);
  }, []);

  const handlePause = useCallback(() => {
    replayRef.current?.pause();
    setIsPlaying(false);
  }, []);

  const handleReset = useCallback(() => {
    replayRef.current?.reset();
    setIsPlaying(false);
    setCurrentTime(0);
  }, []);

  const handleSeek = useCallback((time: number) => {
    replayRef.current?.seek(time);
    setCurrentTime(time);
  }, []);

  const handleSpeedChange = useCallback(
    (speed: number) => {
      onPlaybackSpeedChange?.(speed);
      setShowSpeedMenu(false);
    },
    [onPlaybackSpeedChange]
  );

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Main Replay Area */}
      <div className="flex-1 relative">
        <ClarityReplay
          ref={replayRef}
          {...replayProps}
          onReady={() => {
            setIsReady(true);
            replayProps.onReady?.();
          }}
        />
      </div>

      {/* Timeline */}
      {showTimeline && isReady && (
        <div className="px-6 py-3 border-t border-gray-200">
          <ClarityTimeline
            decodedData={replayProps.decodedData}
            currentTime={currentTime}
            onSeek={handleSeek}
          />
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <button
            onClick={isPlaying ? handlePause : handlePlay}
            disabled={!isReady}
            className="clarity-control-button-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isPlaying ? <Pause size={16} /> : <Play size={16} />}
            <span>{isPlaying ? "Pause" : "Play"}</span>
          </button>

          <button
            onClick={handleReset}
            disabled={!isReady}
            className="clarity-control-button-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RotateCcw size={16} />
            <span>Reset</span>
          </button>
        </div>

        <div className="flex items-center space-x-3">
          {/* Speed Control */}
          {showSettings && (
            <div className="relative">
              <button
                onClick={() => setShowSpeedMenu(!showSpeedMenu)}
                className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              >
                <Settings size={14} />
                <span>{playbackSpeed}x</span>
              </button>

              {showSpeedMenu && (
                <div className="absolute bottom-full right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[80px]">
                  {speedOptions.map((speed) => (
                    <button
                      key={speed}
                      onClick={() => handleSpeedChange(speed)}
                      className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 ${
                        speed === playbackSpeed
                          ? "bg-blue-50 text-blue-600"
                          : "text-gray-700"
                      }`}
                    >
                      {speed}x
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          <button
            onClick={() => {
              const visualizer = replayRef.current?.getVisualizer();
              if (visualizer && replayProps.decodedData) {
                const merged = visualizer.merge(replayProps.decodedData);
                const blob = new Blob([JSON.stringify(merged, null, 2)], {
                  type: "application/json",
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = "clarity-session-data.json";
                a.click();
                URL.revokeObjectURL(url);
              }
            }}
            disabled={!isReady}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download session data"
          >
            <Download size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClarityReplayEnhanced;
```

## Performance Optimizations

### 1. Lazy Loading and Code Splitting

```typescript
// components/LazyReplay.tsx
import dynamic from "next/dynamic";
import { ComponentType } from "react";
import { ClarityReplayProps } from "../types/clarity";

// Lazy load the replay component
const ClarityReplayEnhanced = dynamic(() => import("./ClarityReplayEnhanced"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading replay component...</p>
      </div>
    </div>
  ),
}) as ComponentType<ClarityReplayProps>;

export default ClarityReplayEnhanced;
```

### 2. Memory Management Hook

```typescript
// hooks/useMemoryOptimizedReplay.ts
import { useEffect, useRef, useCallback } from "react";
import { Data } from "clarity-decode";

interface UseMemoryOptimizedReplayOptions {
  maxCacheSize?: number;
  cleanupInterval?: number;
}

export const useMemoryOptimizedReplay = (
  decodedData: Data.DecodedPayload[],
  options: UseMemoryOptimizedReplayOptions = {}
) => {
  const { maxCacheSize = 100, cleanupInterval = 30000 } = options;
  const cacheRef = useRef(new Map());
  const cleanupTimerRef = useRef<NodeJS.Timeout>();

  const cleanup = useCallback(() => {
    const cache = cacheRef.current;
    if (cache.size > maxCacheSize) {
      const entries = Array.from(cache.entries());
      const toDelete = entries.slice(0, entries.length - maxCacheSize);
      toDelete.forEach(([key]) => cache.delete(key));
    }
  }, [maxCacheSize]);

  useEffect(() => {
    // Set up periodic cleanup
    cleanupTimerRef.current = setInterval(cleanup, cleanupInterval);

    return () => {
      if (cleanupTimerRef.current) {
        clearInterval(cleanupTimerRef.current);
      }
      cacheRef.current.clear();
    };
  }, [cleanup, cleanupInterval]);

  const getCachedData = useCallback((key: string) => {
    return cacheRef.current.get(key);
  }, []);

  const setCachedData = useCallback(
    (key: string, data: any) => {
      cacheRef.current.set(key, data);
      cleanup();
    },
    [cleanup]
  );

  return { getCachedData, setCachedData };
};
```

This integration provides a complete solution for embedding Clarity session replays in Next.js applications with proper error handling, TypeScript support, Tailwind CSS styling, and performance considerations.
