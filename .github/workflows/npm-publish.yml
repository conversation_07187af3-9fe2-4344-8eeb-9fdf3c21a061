# This workflow builds and publishes selected libraries

name: Publish to NPM

on:
  push:
    branches:
      - master
    paths:
      - 'packages/clarity-js/src/core/version.ts'

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 22.17.0
      - run: yarn install
      - run: yarn build
      - run: yarn test
      - run: |
          # Make size check script executable
          chmod +x ./scripts/check-file-size.sh
      - run: |
          # Run the size check script for clarity-js
          ../../scripts/check-file-size.sh clarity-js ${{ vars.VERSION_FOR_SIZE_COMPARISON }} "build/clarity.min.js" 2
        working-directory: ./packages/clarity-js
      - run: |
          # Run the size check script for clarity-decode
          ../../scripts/check-file-size.sh clarity-decode ${{ vars.VERSION_FOR_SIZE_COMPARISON }} "build/clarity.decode.min.js" 2
        working-directory: ./packages/clarity-decode
      - run: |
          # Run the size check script for clarity-visualize
          ../../scripts/check-file-size.sh clarity-visualize ${{ vars.VERSION_FOR_SIZE_COMPARISON }} "build/clarity.visualize.min.js" 2
        working-directory: ./packages/clarity-visualize

  publish-npm:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18.20.6
          registry-url: https://registry.npmjs.org/
      - run: yarn install
      - run: yarn build
      - run: npm publish
        working-directory: ./packages/clarity-js
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_ACCESS_TOKEN }}
      - run: npm publish
        working-directory: ./packages/clarity-decode
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_ACCESS_TOKEN }}
      - run: npm publish
        working-directory: ./packages/clarity-visualize
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_ACCESS_TOKEN }}
      - name: Extract version
        id: version
        run: |
          VERSION=$(node -p "const fs = require('fs'); const content = fs.readFileSync('./packages/clarity-js/src/core/version.ts', 'utf8'); content.match(/version = \"([^\"]+)\"/)[1]")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
      - name: Create Git tag
        if: success()
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a "v${{ steps.version.outputs.version }}" -m "Release v${{ steps.version.outputs.version }}"
          git push origin "v${{ steps.version.outputs.version }}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
