{"compilerOptions": {"module": "esnext", "target": "es5", "lib": ["es6", "dom", "es2016", "es2017"], "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@src/*": ["src/*"], "@clarity-types/*": ["types/*"]}}, "include": ["src/**/*.ts", "types/**/*.d.ts", "rollup.config.ts"], "exclude": ["node_modules", "build"]}